<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'ebook' to the product_type enum
        DB::statement("ALTER TABLE product_categories MODIFY COLUMN product_type ENUM('digital', 'course', 'ebook', 'both') DEFAULT 'both'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'ebook' from the product_type enum
        DB::statement("ALTER TABLE product_categories MODIFY COLUMN product_type ENUM('digital', 'course', 'both') DEFAULT 'both'");
    }
};
