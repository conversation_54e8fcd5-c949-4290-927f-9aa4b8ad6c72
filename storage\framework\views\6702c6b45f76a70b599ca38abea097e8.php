

<?php $__env->startSection('head'); ?>
    <!-- Page Context Meta Tags for DigiAI -->
    <meta name="seller-id" content="<?php echo e(Auth::user()->sellerApplication->id ?? ''); ?>">
    <meta name="user-id" content="<?php echo e(Auth::id()); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<style>
    /* Ensure chart containers have proper dimensions */
    canvas {
        display: block;
        box-sizing: border-box;
        height: 100% !important;
        width: 100% !important;
    }
</style>
<div class="space-y-6">
    <div class="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
            <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p class="text-gray-500">Welcome back, <?php echo e(Auth::user()->name ?? 'Seller'); ?>!</p>
        </div>
        <div class="flex items-center gap-2">
            
            <a href="<?php echo e(route('seller.products.select-type')); ?>" class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                <span>Add New Product</span>
            </a>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div class="rounded-lg border bg-white p-6 shadow-sm">
            <div class="flex flex-row items-center justify-between pb-2">
                <h3 class="text-sm font-medium text-gray-500">Total Revenue</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-green-500">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
            </div>
            <div class="text-2xl font-bold">Rp <?php echo e(number_format($totalRevenue ?? 0, 0, ',', '.')); ?></div>
            <p class="text-xs text-gray-500">
                <?php if(($totalRevenue ?? 0) > 0): ?>
                    <span class="text-green-500">+12.5%</span> from last month
                <?php else: ?>
                    No revenue yet
                <?php endif; ?>
            </p>
        </div>
        <div class="rounded-lg border bg-white p-6 shadow-sm">
            <div class="flex flex-row items-center justify-between pb-2">
                <h3 class="text-sm font-medium text-gray-500">Total Sales</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-indigo-600">
                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                    <path d="M2 10h20"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold"><?php echo e($totalSales ?? 0); ?></div>
            <p class="text-xs text-gray-500">
                <?php if(($totalSales ?? 0) > 0): ?>
                    <span class="text-green-500">+8.2%</span> from last month
                <?php else: ?>
                    No sales yet
                <?php endif; ?>
            </p>
        </div>
        <div class="rounded-lg border bg-white p-6 shadow-sm">
            <div class="flex flex-row items-center justify-between pb-2">
                <h3 class="text-sm font-medium text-gray-500">Active Products</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-purple-500">
                    <path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold"><?php echo e($activeProducts ?? 0); ?></div>
            <p class="text-xs text-gray-500">
                <?php if(($activeProducts ?? 0) > 0): ?>
                    <span class="text-green-500">+2</span> new this month
                <?php else: ?>
                    No active products
                <?php endif; ?>
            </p>
        </div>
        <div class="rounded-lg border bg-white p-6 shadow-sm">
            <div class="flex flex-row items-center justify-between pb-2">
                <h3 class="text-sm font-medium text-gray-500">Avg. Rating</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-yellow-500">
                    <path d="M12 2L15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold"><?php echo e($avgRating ?? 0); ?></div>
            <p class="text-xs text-gray-500">
                <?php if(($avgRating ?? 0) > 0): ?>
                    <span class="text-green-500">+0.2</span> from last month
                <?php else: ?>
                    No ratings yet
                <?php endif; ?>
            </p>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-7">
        <div class="rounded-lg border bg-white shadow-sm md:col-span-4">
            <div class="flex flex-row items-center justify-between border-b p-6">
                <div>
                    <h3 class="text-lg font-medium">Cumulative Revenue Growth</h3>
                    <p class="text-sm text-gray-500">Your total revenue growth over time</p>
                </div>
                <div class="ml-auto">
                    <div class="inline-flex rounded-md shadow-sm">
                        <a href="<?php echo e(route('seller.dashboard', ['range' => '7d'])); ?>" class="rounded-l-md border border-gray-300 <?php echo e(($timeRange ?? '7d') === '7d' ? 'bg-indigo-50 text-indigo-700' : 'bg-white text-gray-700'); ?> px-4 py-2 text-sm font-medium hover:bg-gray-50">7D</a>
                        <a href="<?php echo e(route('seller.dashboard', ['range' => '30d'])); ?>" class="border border-gray-300 <?php echo e(($timeRange ?? '7d') === '30d' ? 'bg-indigo-50 text-indigo-700' : 'bg-white text-gray-700'); ?> px-4 py-2 text-sm font-medium hover:bg-gray-50">30D</a>
                        <a href="<?php echo e(route('seller.dashboard', ['range' => '90d'])); ?>" class="rounded-r-md border border-gray-300 <?php echo e(($timeRange ?? '7d') === '90d' ? 'bg-indigo-50 text-indigo-700' : 'bg-white text-gray-700'); ?> px-4 py-2 text-sm font-medium hover:bg-gray-50">90D</a>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="w-full rounded-lg bg-white" style="height: 300px;">
                    <canvas id="revenueChart" width="100%" height="100%"></canvas>
                </div>
            </div>
        </div>
        <div class="rounded-lg border bg-white shadow-sm md:col-span-3">
            <div class="border-b p-6">
                <h3 class="text-lg font-medium">Recent Successful Sales</h3>
                <p class="text-sm text-gray-500">You sold <?php echo e(($recentSales ?? collect())->count()); ?> products successfully</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php if(($recentSales ?? collect())->isEmpty()): ?>
                        <p class="text-sm text-gray-500 text-center">No recent sales</p>
                    <?php else: ?>
                        <?php $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center gap-4">
                            <div class="h-12 w-12 rounded-md bg-gray-100">
                                <img src="<?php echo e($sale->product ? asset('images/placeholder.jpg') : asset('images/placeholder.jpg')); ?>" alt="<?php echo e($sale->product->name ?? 'Product'); ?>" class="h-12 w-12 rounded-md object-cover">
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium"><?php echo e($sale->product->name ?? 'Deleted Product'); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($sale->created_at->format('g:i A')); ?></p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium">Rp <?php echo e(number_format($sale->amount ?? 0, 0, ',', '.')); ?></p>
                                <span class="inline-flex items-center rounded-full border border-green-100 bg-green-50 px-2 py-0.5 text-xs font-medium text-green-700">
                                    Completed
                                </span>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                    <a href="<?php echo e(route('seller.orders.index')); ?>" class="inline-flex w-full items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        View all orders
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 h-4 w-4">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div class="rounded-lg border bg-white shadow-sm lg:col-span-2">
            <div class="border-b p-6">
                <h3 class="text-lg font-medium">Top Selling Products</h3>
                <p class="text-sm text-gray-500">Your best performing products (successful orders only)</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php if(($topSellingProducts ?? collect())->isEmpty()): ?>
                        <p class="text-sm text-gray-500 text-center">No top-selling products</p>
                    <?php else: ?>
                        <?php $__currentLoopData = $topSellingProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="grid grid-cols-3 gap-4 rounded-lg border p-4">
                            <div class="col-span-2 flex items-center gap-4">
                                <div class="h-16 w-16 rounded-md bg-gray-100">
                                    <img src="<?php echo e(asset('images/placeholder.jpg')); ?>" alt="<?php echo e($product->name); ?>" class="h-16 w-16 rounded-md object-cover">
                                </div>
                                <div>
                                    <p class="font-medium"><?php echo e($product->name); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e(ucfirst($product->category) ?? 'Uncategorized'); ?></p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end justify-center">
                                <p class="text-xl font-bold">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></p>
                                <p class="text-sm text-gray-500"><?php echo e($product->orders_count); ?> sales</p>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="rounded-lg border bg-white shadow-sm">
            <div class="border-b p-6">
                <h3 class="text-lg font-medium">Quick Actions</h3>
                <p class="text-sm text-gray-500">Common tasks and actions</p>
            </div>
            <div class="p-6">
                <div class="grid gap-2">
                    <a href="<?php echo e(route('seller.products.select-type')); ?>" class="inline-flex items-center justify-start rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M5 12h14"></path>
                            <path d="M12 5v14"></path>
                        </svg>
                        Add New Product
                    </a>
                    <a href="<?php echo e(route('seller.orders.index')); ?>" class="inline-flex items-center justify-start rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                            <path d="M2 10h20"></path>
                        </svg>
                        View Recent Orders
                    </a>
                    <a href="<?php echo e(route('seller.analytics')); ?>" class="inline-flex items-center justify-start rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M3 3v18h18"></path>
                            <path d="m19 9-5 5-4-4-3 3"></path>
                        </svg>
                        View Analytics
                    </a>
                    <a href="<?php echo e(route('seller.settings')); ?>" class="inline-flex items-center justify-start rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        Update Store Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Wait for the DOM to be fully loaded
        window.addEventListener('load', function() {
            console.log('Window loaded, initializing dashboard chart...');

            try {
                // Revenue Chart
                const revenueCanvas = document.getElementById('revenueChart');
                if (!revenueCanvas) {
                    console.error('Revenue chart canvas not found');
                    return;
                }

                const revenueCtx = revenueCanvas.getContext('2d');
                const revenueData = <?php echo json_encode($revenueData ?? [], 15, 512) ?>;
                console.log('Revenue data:', revenueData);

                // Format dates and prepare data for chart
                const revenueLabels = [];
                const revenueValues = [];
                let cumulativeRevenue = 0;

                revenueData.forEach(item => {
                    try {
                        const date = new Date(item.date);
                        revenueLabels.push(date.toLocaleDateString('id-ID', {
                            day: 'numeric',
                            month: 'short'
                        }));
                        // Add current day's revenue to the cumulative total
                        cumulativeRevenue += parseFloat(item.revenue) || 0;
                        // Push the cumulative total to the values array
                        revenueValues.push(cumulativeRevenue);
                    } catch (e) {
                        console.error('Error processing revenue data item:', item, e);
                    }
                });

                // Create gradient for revenue chart
                const revenueGradient = revenueCtx.createLinearGradient(0, 0, 0, 400);
                revenueGradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
                revenueGradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');

                // Create revenue chart
                const revenueChart = new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: revenueLabels,
                        datasets: [{
                            label: 'Cumulative Revenue (Rp)',
                            data: revenueValues,
                            borderColor: '#4F46E5',
                            backgroundColor: revenueGradient,
                            borderWidth: 2,
                            pointBackgroundColor: '#4F46E5',
                            pointRadius: 3,
                            pointHoverRadius: 5,
                            tension: 0.3,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let value = context.raw;
                                        return 'Total Revenue: Rp ' + value.toLocaleString('id-ID');
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        if (value >= 1000000) {
                                            return 'Rp ' + (value / 1000000).toFixed(1) + ' jt';
                                        } else if (value >= 1000) {
                                            return 'Rp ' + (value / 1000).toFixed(1) + ' rb';
                                        }
                                        return 'Rp ' + value;
                                    }
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error initializing dashboard chart:', error);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('seller.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/dashboard.blade.php ENDPATH**/ ?>