<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($seller->store_name); ?> - Digitora</title>
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('images/digitora-logo.png')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- AI Chat CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/ai-chat.css')); ?>">

    <!-- Store-specific CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('dev-js/store.css')); ?>">

    
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Scripts -->
    
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <style>
        /* Custom styles for improved design */
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
            --secondary-gradient: linear-gradient(135deg, #4f46e5 0%, #9333ea 100%);
            --primary-color: #6366f1;
            --secondary-color: #a855f7;
            --transition-speed: 0.3s;
        }

        body {
            background-color: #f9fafb;
        }

        /* Header and Navigation Styles */
        .hero-gradient {
            background: var(--primary-gradient);
            box-shadow: 0 4px 15px -3px rgba(99, 102, 241, 0.15);
        }

        .nav-link {
            position: relative;
            transition: all var(--transition-speed) ease;
            padding-bottom: 4px;
            margin-bottom: -4px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-gradient);
            transition: width var(--transition-speed) ease;
            border-radius: 2px;
        }

        .nav-link:hover::after {
            width: 70%;
        }

        .nav-link.active::after {
            width: 70%;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }

        /* Card Animations */
        .card-hover {
            transition: transform var(--transition-speed) ease,
                box-shadow var(--transition-speed) ease,
                border-color var(--transition-speed) ease;
            border: 1px solid transparent;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.1), 0 10px 20px -10px rgba(0, 0, 0, 0.04);
            border-color: rgba(99, 102, 241, 0.1);
        }

        /* Scroll Animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.7s ease, transform 0.7s ease;
        }

        .animate-on-scroll.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Cart Dropdown Styles */
        .cart-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(10px);
            visibility: hidden;
            transition: opacity var(--transition-speed) ease, transform var(--transition-speed) ease, visibility var(--transition-speed) ease;
            z-index: 50;
            overflow: hidden;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .cart-container:hover .cart-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* Mobile Menu Enhancements */
        .mobile-menu-container {
            transition: max-height 0.5s ease;
            max-height: 0;
            overflow: hidden;
        }

        .mobile-menu-container.open {
            max-height: 500px;
        }

        .mobile-menu-item {
            transform: translateX(-10px);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
            transition-delay: calc(var(--index) * 0.05s);
        }

        .mobile-menu-container.open .mobile-menu-item {
            transform: translateX(0);
            opacity: 1;
        }

        /* Mobile Navigation Improvements */
        @media (max-width: 1023px) {
            .store-nav {
                display: none;
            }
        }

        @media (max-width: 640px) {
            .store-brand {
                flex: 1;
                min-width: 0;
            }

            .store-brand span {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        /* Button Styles */
        .btn {
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: -100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
            transition: left 0.6s ease;
        }

        .btn:hover::after {
            left: 100%;
        }

        /* Footer Enhancements */
        .footer-link {
            transition: all var(--transition-speed) ease;
            position: relative;
            display: inline-block;
        }

        .footer-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 1px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-color);
            transition: width var(--transition-speed) ease;
        }

        .footer-link:hover::after {
            width: 100%;
        }

        .social-icon {
            transition: all var(--transition-speed) ease;
        }

        .social-icon:hover {
            transform: translateY(-3px);
            color: var(--primary-color);
        }
    </style>
</head>

<body class="font-sans antialiased bg-gray-50">
    <!-- Enhanced Store Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50 border-b border-gray-100">
        <!-- Top Bar with Store Branding -->
        <div class="store-header bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-3">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between">
                    <!-- Store Brand Identity -->
                    <div class="store-brand">
                        <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>" class="flex items-center gap-3">
                            <?php if($seller->store_logo): ?>
                                <img src="<?php echo e(route('store.logo', $seller->store_slug)); ?>" alt="<?php echo e($seller->store_name); ?>"
                                    class="store-logo h-12 w-12 rounded-xl object-cover shadow-lg border-2 border-white/20">
                            <?php else: ?>
                                <div class="store-logo-placeholder h-12 w-12 bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-xl flex items-center justify-center">
                                    <span class="text-xl font-bold text-white"><?php echo e(substr($seller->store_name, 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>
                            <div class="flex flex-col">
                                <span class="text-xl font-bold text-white tracking-tight"><?php echo e($seller->store_name); ?></span>
                                <span class="text-sm text-indigo-100 font-medium">Digital Store</span>
                            </div>
                        </a>
                    </div>

                    <!-- Store Actions -->
                    <div class="flex items-center gap-4">
                        <!-- Membership Badge -->
                        <?php if($sellerMembershipTier && $sellerMembershipTier->slug !== 'starter'): ?>
                            <div class="hidden sm:flex items-center px-3 py-1.5 rounded-full text-xs font-bold shadow-lg backdrop-blur-sm <?php echo e($sellerMembershipTier->slug === 'basic' ? 'bg-white/20 text-white border border-white/30' : 'bg-yellow-400/90 text-yellow-900 border border-yellow-300'); ?>">
                                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <?php echo e($sellerMembershipTier->slug === 'basic' ? 'VERIFIED' : 'PREMIUM'); ?>

                            </div>
                        <?php endif; ?>

                        <!-- Edit Store Button (Only visible to store owner) -->
                        <?php if(auth()->guard()->check()): ?>
                            <?php if(Auth::user()->isSeller() && Auth::user()->id === $seller->id): ?>
                                <a href="<?php echo e(route('seller.settings')); ?>" class="hidden sm:inline-flex items-center rounded-lg bg-white/10 backdrop-blur-sm px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-all duration-200 border border-white/20">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    Edit Store
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="bg-white">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between h-16">

                    <?php
                        // Detect if we're on the main store page or a subpage
                        $isMainStorePage = request()->routeIs('store.show') && !request()->route('category');
                        $isProductDetailPage = request()->routeIs('store.product');
                        $isStoreSubpage = !$isMainStorePage; // Any page that's not the main store page
                    ?>

                    <!-- Enhanced Navigation -->
                    <nav class="store-nav hidden lg:flex">
                        <a href="<?php echo e(route('home')); ?>" class="store-nav-link">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            Main Site
                        </a>
                        <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>"
                           class="store-nav-link <?php echo e($isMainStorePage ? 'active' : ''); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            Store Home
                        </a>

                        <?php if($isMainStorePage): ?>
                            
                            <a href="#products"
                               class="store-nav-link <?php echo e(request()->route('category') ? 'active' : ''); ?>">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                                Products
                            </a>
                            <a href="#courses" class="store-nav-link">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                Courses
                            </a>
                        <?php else: ?>
                            
                            <a href="<?php echo e(route('browse.products')); ?>" class="store-nav-link">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                                Products
                            </a>
                            <a href="<?php echo e(route('browse.courses')); ?>" class="store-nav-link">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                Courses
                            </a>
                        <?php endif; ?>

                        <a href="#about-store" class="store-nav-link">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            About
                        </a>
                    </nav>

                <?php
                    // Define cart count variable
                    $cartCount = 0;
                    if (Auth::check()) {
                        $cart = \App\Models\Cart::where('user_id', Auth::id())->first();
                        if ($cart) {
                            $cartCount = $cart->items->sum('quantity');
                        }
                    } else {
                        $sessionId = session()->get('cart_session_id');
                        if ($sessionId) {
                            $cart = \App\Models\Cart::where('session_id', $sessionId)->first();
                            if ($cart) {
                                $cartCount = $cart->items->sum('quantity');
                            }
                        }
                    }
                ?>

                    <!-- Enhanced Search and Cart -->
                    <div class="hidden lg:flex items-center space-x-4">
                        <!-- Professional Search -->
                        <form action="<?php echo e(route('store.search', $seller->store_slug)); ?>" method="GET" class="relative">
                            <input type="text" name="query" placeholder="Search products..." required
                                class="w-64 pl-10 pr-4 py-2.5 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm bg-gray-50 hover:bg-white transition-colors duration-200">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </form>

                        <!-- Enhanced Cart Button -->
                        <a href="<?php echo e(route('cart.index')); ?>" class="relative group">
                            <div class="flex items-center gap-2 px-4 py-2.5 rounded-xl bg-gray-50 hover:bg-indigo-50 transition-all duration-200 border border-gray-200 hover:border-indigo-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                <span class="text-sm font-medium text-gray-700 group-hover:text-indigo-700">Cart</span>
                                <?php if($cartCount > 0): ?>
                                    <span class="bg-indigo-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold"><?php echo e($cartCount); ?></span>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>

                    <!-- Mobile Actions -->
                    <div class="flex items-center lg:hidden space-x-3">
                        <!-- Mobile Cart -->
                        <a href="<?php echo e(route('cart.index')); ?>" class="relative p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <?php if($cartCount > 0): ?>
                                <span class="absolute -top-1 -right-1 bg-indigo-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold"><?php echo e($cartCount); ?></span>
                            <?php endif; ?>
                        </a>

                        <!-- Mobile Menu Button -->
                        <button type="button" data-mobile-menu-toggle class="p-3 rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 touch-manipulation" style="min-width: 44px; min-height: 44px;">
                            <svg class="h-6 w-6 text-gray-600 menu-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                            <svg class="h-6 w-6 text-gray-600 close-icon hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

        <!-- Enhanced Mobile Menu -->
        <div class="lg:hidden hidden bg-white border-t border-gray-200 shadow-lg" id="mobile-menu">
            <div class="px-4 pt-4 pb-3 space-y-2">
                <!-- Mobile Navigation Links -->
                <a href="<?php echo e(route('home')); ?>" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    Main Site
                </a>

                <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 <?php echo e($isMainStorePage ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-indigo-50 hover:text-indigo-600'); ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    Store Home
                </a>

                <?php if($isMainStorePage): ?>
                    
                    <a href="#products" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 <?php echo e(request()->route('category') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-indigo-50 hover:text-indigo-600'); ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        Products
                    </a>

                    <a href="#courses" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        Courses
                    </a>
                <?php else: ?>
                    
                    <a href="<?php echo e(route('browse.products')); ?>" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        Products
                    </a>

                    <a href="<?php echo e(route('browse.courses')); ?>" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        Courses
                    </a>
                <?php endif; ?>

                <a href="#about-store" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    About
                </a>

                <!-- Edit Store Button (Only visible to store owner) - Mobile -->
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->isSeller() && Auth::user()->id === $seller->id): ?>
                        <a href="<?php echo e(route('seller.settings')); ?>" class="flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-indigo-600 bg-indigo-50 hover:bg-indigo-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit Store
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Mobile Search -->
            <div class="px-4 py-3 border-t border-gray-100">
                <form action="<?php echo e(route('store.search', $seller->store_slug)); ?>" method="GET" class="relative">
                    <input type="text" name="query" placeholder="Search products..." required class="w-full pl-10 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm bg-gray-50">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </form>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Enhanced Store Footer -->
    <footer class="store-footer bg-gradient-to-br from-gray-50 to-gray-100 border-t border-gray-200 mt-16">
        <!-- Main Footer Content -->
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Store Brand Section -->
                <div class="md:col-span-2">
                    <div class="store-brand mb-6">
                        <?php if($seller->store_logo): ?>
                            <img src="<?php echo e(route('store.logo', $seller->store_slug)); ?>" alt="<?php echo e($seller->store_name); ?>"
                                 class="store-logo h-16 w-16 rounded-xl object-cover shadow-lg border-2 border-white">
                        <?php else: ?>
                            <div class="store-logo-placeholder h-16 w-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg border-2 border-white">
                                <span class="text-2xl font-bold text-white"><?php echo e(substr($seller->store_name, 0, 1)); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="ml-4">
                            <h3 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e($seller->store_name); ?></h3>
                            <div class="flex items-center gap-2 mb-3">
                                <span class="text-sm font-medium text-gray-600">Professional Digital Store</span>
                                <?php if($sellerMembershipTier && $sellerMembershipTier->slug !== 'starter'): ?>
                                    <div class="flex items-center px-2 py-1 rounded-full text-xs font-bold <?php echo e($sellerMembershipTier->slug === 'basic' ? 'bg-gray-100 text-gray-700' : 'bg-yellow-100 text-yellow-800'); ?>">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <?php echo e($sellerMembershipTier->slug === 'basic' ? 'VERIFIED' : 'PREMIUM'); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed max-w-md">
                        <?php echo e($seller->store_description ?? 'Welcome to our professional digital store. We offer high-quality digital products and courses designed to help you succeed in your business and personal goals.'); ?>

                    </p>

                    <!-- Store Stats -->
                    <div class="mt-6 grid grid-cols-2 gap-4 max-w-sm">
                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div class="text-2xl font-bold text-indigo-600"><?php echo e($products->total() ?? 0); ?></div>
                            <div class="text-sm text-gray-600">Digital Products</div>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div class="text-2xl font-bold text-purple-600"><?php echo e($courses->total() ?? 0); ?></div>
                            <div class="text-sm text-gray-600">Online Courses</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Links</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="<?php echo e(route('home')); ?>" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                Main Site
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                Store Home
                            </a>
                        </li>
                        <?php if($isMainStorePage): ?>
                            
                            <li>
                                <a href="#products" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                    Products
                                </a>
                            </li>
                            <li>
                                <a href="#courses" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                    Courses
                                </a>
                            </li>
                        <?php else: ?>
                            
                            <li>
                                <a href="<?php echo e(route('browse.products')); ?>" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                    Products
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo e(route('browse.courses')); ?>" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                    Courses
                                </a>
                            </li>
                        <?php endif; ?>
                        <li>
                            <a href="<?php echo e(route('cart.index')); ?>" class="store-footer-link text-gray-600 hover:text-indigo-600 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Shopping Cart
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Contact Information -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Get in Touch</h4>
                    <ul class="space-y-4">
                        <?php if($seller->email): ?>
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">Email</p>
                                    <p class="text-sm text-gray-600"><?php echo e($seller->email); ?></p>
                                </div>
                            </li>
                        <?php endif; ?>
                        <?php if($seller->phone): ?>
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">Phone</p>
                                    <p class="text-sm text-gray-600"><?php echo e($seller->phone); ?></p>
                                </div>
                            </li>
                        <?php endif; ?>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">Support Hours</p>
                                <p class="text-sm text-gray-600">Mon - Fri, 9 AM - 5 PM</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-200 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="md:flex md:items-center md:justify-between">
                    <div class="flex items-center space-x-4">
                        <p class="text-sm text-gray-600">
                            &copy; <?php echo e(date('Y')); ?> <?php echo e($seller->store_name); ?>. All rights reserved.
                        </p>
                        <span class="text-gray-300">|</span>
                        <p class="text-sm text-gray-500">
                            Powered by <span class="font-semibold text-indigo-600">Digitora</span>
                        </p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <p class="text-xs text-gray-500">
                            Professional digital marketplace for Indonesian entrepreneurs
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- AI Chat JS -->
    <script src="<?php echo e(asset(js_path() . '/ai-chat.js')); ?>" defer></script>

    <!-- Store-specific JavaScript -->
    <script src="<?php echo e(asset('dev-js/store.js')); ?>" defer></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/store/layout.blade.php ENDPATH**/ ?>