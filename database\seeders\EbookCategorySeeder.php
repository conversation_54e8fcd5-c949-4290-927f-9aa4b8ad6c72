<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class EbookCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating ebook-specific categories...');

        // Define ebook-specific categories structure
        $ebookCategories = [
            [
                'name' => 'Fiction & Literature',
                'icon' => 'book-open',
                'description' => 'Fiction books, novels, and literary works.',
                'subcategories' => [
                    [
                        'name' => 'Romance',
                        'description' => 'Romance novels and love stories.',
                        'legacy_code' => 'ebook_romance',
                        'detailed_categories' => [
                            ['name' => 'Contemporary Romance', 'description' => 'Modern romance stories.'],
                            ['name' => 'Historical Romance', 'description' => 'Romance set in historical periods.'],
                            ['name' => 'Fantasy Romance', 'description' => 'Romance with fantasy elements.'],
                            ['name' => 'Young Adult Romance', 'description' => 'Romance for young adult readers.'],
                        ]
                    ],
                    [
                        'name' => 'Mystery & Thriller',
                        'description' => 'Mystery, thriller, and suspense novels.',
                        'legacy_code' => 'ebook_mystery',
                        'detailed_categories' => [
                            ['name' => 'Crime Fiction', 'description' => 'Crime and detective stories.'],
                            ['name' => 'Psychological Thriller', 'description' => 'Psychological suspense novels.'],
                            ['name' => 'Cozy Mystery', 'description' => 'Light-hearted mystery stories.'],
                            ['name' => 'Police Procedural', 'description' => 'Police investigation stories.'],
                        ]
                    ],
                    [
                        'name' => 'Science Fiction & Fantasy',
                        'description' => 'Science fiction and fantasy novels.',
                        'legacy_code' => 'ebook_scifi',
                        'detailed_categories' => [
                            ['name' => 'Space Opera', 'description' => 'Epic space adventure stories.'],
                            ['name' => 'Urban Fantasy', 'description' => 'Fantasy set in modern urban settings.'],
                            ['name' => 'Dystopian Fiction', 'description' => 'Stories set in dystopian futures.'],
                            ['name' => 'Epic Fantasy', 'description' => 'High fantasy with epic scope.'],
                        ]
                    ],
                    [
                        'name' => 'Literary Fiction',
                        'description' => 'Literary and contemporary fiction.',
                        'legacy_code' => 'ebook_literary',
                        'detailed_categories' => [
                            ['name' => 'Contemporary Fiction', 'description' => 'Modern literary fiction.'],
                            ['name' => 'Historical Fiction', 'description' => 'Fiction set in historical periods.'],
                            ['name' => 'Short Stories', 'description' => 'Collections of short stories.'],
                            ['name' => 'Experimental Fiction', 'description' => 'Innovative and experimental narratives.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Non-Fiction & Education',
                'icon' => 'academic-cap',
                'description' => 'Educational content, guides, and informational books.',
                'subcategories' => [
                    [
                        'name' => 'Business & Entrepreneurship',
                        'description' => 'Business guides and entrepreneurship books.',
                        'legacy_code' => 'ebook_business',
                        'detailed_categories' => [
                            ['name' => 'Startup Guides', 'description' => 'Guides for starting a business.'],
                            ['name' => 'Marketing & Sales', 'description' => 'Marketing and sales strategies.'],
                            ['name' => 'Leadership & Management', 'description' => 'Leadership and management skills.'],
                            ['name' => 'Finance & Investment', 'description' => 'Financial planning and investment guides.'],
                        ]
                    ],
                    [
                        'name' => 'Self-Help & Personal Development',
                        'description' => 'Self-improvement and personal growth books.',
                        'legacy_code' => 'ebook_selfhelp',
                        'detailed_categories' => [
                            ['name' => 'Productivity & Time Management', 'description' => 'Productivity improvement guides.'],
                            ['name' => 'Mindfulness & Meditation', 'description' => 'Mindfulness and meditation practices.'],
                            ['name' => 'Motivation & Success', 'description' => 'Motivational and success guides.'],
                            ['name' => 'Relationships & Communication', 'description' => 'Relationship and communication skills.'],
                        ]
                    ],
                    [
                        'name' => 'Health & Wellness',
                        'description' => 'Health, fitness, and wellness guides.',
                        'legacy_code' => 'ebook_health',
                        'detailed_categories' => [
                            ['name' => 'Fitness & Exercise', 'description' => 'Fitness and exercise guides.'],
                            ['name' => 'Nutrition & Diet', 'description' => 'Nutrition and diet plans.'],
                            ['name' => 'Mental Health', 'description' => 'Mental health and wellness guides.'],
                            ['name' => 'Alternative Medicine', 'description' => 'Alternative and holistic health approaches.'],
                        ]
                    ],
                    [
                        'name' => 'Technology & Programming',
                        'description' => 'Technical guides and programming books.',
                        'legacy_code' => 'ebook_tech',
                        'detailed_categories' => [
                            ['name' => 'Web Development', 'description' => 'Web development guides and tutorials.'],
                            ['name' => 'Mobile App Development', 'description' => 'Mobile app development guides.'],
                            ['name' => 'Data Science & AI', 'description' => 'Data science and artificial intelligence guides.'],
                            ['name' => 'Cybersecurity', 'description' => 'Cybersecurity and information security guides.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Academic & Professional',
                'icon' => 'briefcase',
                'description' => 'Academic textbooks and professional reference materials.',
                'subcategories' => [
                    [
                        'name' => 'Textbooks & Study Guides',
                        'description' => 'Academic textbooks and study materials.',
                        'legacy_code' => 'ebook_textbook',
                        'detailed_categories' => [
                            ['name' => 'Mathematics & Science', 'description' => 'Math and science textbooks.'],
                            ['name' => 'Language & Literature', 'description' => 'Language and literature study guides.'],
                            ['name' => 'History & Social Studies', 'description' => 'History and social studies materials.'],
                            ['name' => 'Test Preparation', 'description' => 'Test prep and exam study guides.'],
                        ]
                    ],
                    [
                        'name' => 'Professional Reference',
                        'description' => 'Professional handbooks and reference materials.',
                        'legacy_code' => 'ebook_reference',
                        'detailed_categories' => [
                            ['name' => 'Legal Guides', 'description' => 'Legal reference and guides.'],
                            ['name' => 'Medical Reference', 'description' => 'Medical and healthcare reference.'],
                            ['name' => 'Engineering & Technical', 'description' => 'Engineering and technical references.'],
                            ['name' => 'Industry Standards', 'description' => 'Industry-specific standards and practices.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Creative & Arts',
                'icon' => 'palette',
                'description' => 'Creative writing, arts, and hobby-related ebooks.',
                'subcategories' => [
                    [
                        'name' => 'Art & Design',
                        'description' => 'Art, design, and creative guides.',
                        'legacy_code' => 'ebook_art',
                        'detailed_categories' => [
                            ['name' => 'Drawing & Illustration', 'description' => 'Drawing and illustration tutorials.'],
                            ['name' => 'Photography', 'description' => 'Photography guides and techniques.'],
                            ['name' => 'Graphic Design', 'description' => 'Graphic design tutorials and guides.'],
                            ['name' => 'Digital Art', 'description' => 'Digital art and design guides.'],
                        ]
                    ],
                    [
                        'name' => 'Writing & Publishing',
                        'description' => 'Writing guides and publishing resources.',
                        'legacy_code' => 'ebook_writing',
                        'detailed_categories' => [
                            ['name' => 'Creative Writing', 'description' => 'Creative writing techniques and guides.'],
                            ['name' => 'Technical Writing', 'description' => 'Technical and business writing guides.'],
                            ['name' => 'Self-Publishing', 'description' => 'Self-publishing and marketing guides.'],
                            ['name' => 'Copywriting', 'description' => 'Copywriting and content creation guides.'],
                        ]
                    ],
                    [
                        'name' => 'Hobbies & Crafts',
                        'description' => 'Hobby guides and craft instructions.',
                        'legacy_code' => 'ebook_hobbies',
                        'detailed_categories' => [
                            ['name' => 'Cooking & Recipes', 'description' => 'Cookbooks and recipe collections.'],
                            ['name' => 'Gardening', 'description' => 'Gardening guides and plant care.'],
                            ['name' => 'DIY & Home Improvement', 'description' => 'DIY projects and home improvement guides.'],
                            ['name' => 'Travel Guides', 'description' => 'Travel guides and destination information.'],
                        ]
                    ],
                ]
            ],
        ];

        // Create the ebook categories
        $this->createEbookCategories($ebookCategories);

        $this->command->info('Ebook categories created successfully!');
    }

    /**
     * Create ebook categories with their subcategories and detailed categories.
     */
    private function createEbookCategories(array $categories): void
    {
        foreach ($categories as $index => $categoryData) {
            $slug = Str::slug($categoryData['name']);

            // Check if the slug already exists
            $counter = 1;
            $originalSlug = $slug;
            while (ProductCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the main category
            $category = ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => $slug,
                'description' => $categoryData['description'] ?? null,
                'icon' => $categoryData['icon'] ?? null,
                'product_type' => 'ebook', // Mark as ebook category
                'sort_order' => $index,
                'is_active' => true,
            ]);

            // Create subcategories if they exist
            if (isset($categoryData['subcategories']) && is_array($categoryData['subcategories'])) {
                $this->createEbookSubcategories($categoryData['subcategories'], $category->id);
            }
        }
    }

    /**
     * Create ebook subcategories with their detailed categories.
     */
    private function createEbookSubcategories(array $subcategories, string $categoryId): void
    {
        foreach ($subcategories as $index => $subcategoryData) {
            $slug = Str::slug($subcategoryData['name']);

            // Check if the slug already exists anywhere in the table
            $counter = 1;
            $originalSlug = $slug;
            while (ProductSubcategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the subcategory
            $subcategory = ProductSubcategory::create([
                'category_id' => $categoryId,
                'name' => $subcategoryData['name'],
                'slug' => $slug,
                'description' => $subcategoryData['description'] ?? null,
                'icon' => $subcategoryData['icon'] ?? null,
                'legacy_code' => $subcategoryData['legacy_code'] ?? null,
                'sort_order' => $index,
                'is_active' => true,
            ]);

            // Create detailed categories if they exist
            if (isset($subcategoryData['detailed_categories']) && is_array($subcategoryData['detailed_categories'])) {
                $this->createEbookDetailedCategories($subcategoryData['detailed_categories'], $subcategory->id);
            }
        }
    }

    /**
     * Create ebook detailed categories.
     */
    private function createEbookDetailedCategories(array $detailedCategories, string $subcategoryId): void
    {
        foreach ($detailedCategories as $index => $detailedCategoryData) {
            $slug = Str::slug($detailedCategoryData['name']);

            // Check if the slug already exists anywhere in the table
            $counter = 1;
            $originalSlug = $slug;
            while (ProductDetailedCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the detailed category
            ProductDetailedCategory::create([
                'subcategory_id' => $subcategoryId,
                'name' => $detailedCategoryData['name'],
                'slug' => $slug,
                'description' => $detailedCategoryData['description'] ?? null,
                'sort_order' => $index,
                'is_active' => true,
            ]);
        }
    }
}
