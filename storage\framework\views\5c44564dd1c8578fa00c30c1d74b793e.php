<?php
    use Illuminate\Support\Facades\Auth;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="Digitora - Indonesia's premier digital marketplace and learning platform. Discover premium digital products, online courses, and creator stores. Empowering Indonesian entrepreneurs and learners.">
    <meta name="keywords"
        content="digital products Indonesia, online courses Indonesia, eBooks, templates, UMKM, Indonesian marketplace, creators, entrepreneurs, digital learning, kursus online">
    <meta name="author" content="Digitora Team">
    <meta name="robots" content="index, follow">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="geo.region" content="ID">
    <meta name="geo.country" content="Indonesia">
    <meta name="language" content="Indonesian">
    <meta property="og:title" content="<?php echo e($title ?? 'Digitora - Digital Marketplace & Learning Platform Indonesia'); ?>">
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('images/digitora-logo.png')); ?>">
    <meta property="og:description"
        content="Discover premium digital products, online courses, and creator stores on Digitora. Indonesia's leading platform for digital entrepreneurs and learners.">
    <meta property="og:image" content="<?php echo e(asset('images/og-image.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="id_ID">
    <meta property="og:site_name" content="Digitora">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo e($title ?? 'Digitora - Digital Marketplace & Learning Platform Indonesia'); ?>">
    <meta name="twitter:description" content="Discover premium digital products, online courses, and creator stores on Digitora. Indonesia's leading platform for digital entrepreneurs and learners.">
    <meta name="twitter:image" content="<?php echo e(asset('images/og-image.jpg')); ?>">
    <title><?php echo e($title ?? 'Digitora - Digital Marketplace & Learning Platform Indonesia'); ?></title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Vite for Breeze's default assets -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <!-- Custom CSS -->
    
    <link rel="stylesheet" href="<?php echo e(asset('css/seller.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/auth.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/ai-chat.css')); ?>">

</head>

<body class="font-inter antialiased bg-gray-50">
    <!-- Header -->
    <header class="bg-white fixed top-0 left-0 right-0 z-50 transition-shadow duration-300">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Top Deck: Logo, Search, Auth -->
            <div class="flex justify-between items-center h-16 md:h-20">
                <!-- Logo -->
                <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-3" aria-label="Digitora Homepage">
                    <div
                        class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white w-10 h-10 rounded-lg flex items-center justify-center text-2xl font-bold">
                        D</div>
                    <span class="text-2xl font-extrabold text-gray-900 tracking-tight">Digitora</span>
                </a>

                <!-- Search -->
                <div class="relative flex-1 max-w-md mx-4">
                    <form action="<?php echo e(route('user.browse')); ?>" method="GET">
                        <input type="search" name="q" placeholder="Search digital products..."
                            class="w-full border border-gray-200 rounded-full py-2.5 px-5 text-gray-700 text-sm focus:outline-none focus:ring-2 focus:ring-purple-300 focus:border-transparent transition-all duration-200"
                            aria-label="Search digital products">
                        <button type="submit" class="absolute right-4 top-3 bg-transparent border-0 p-0">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </form>
                </div>

                <!-- Auth -->
                <div class="flex items-center space-x-4">
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('cart.index')); ?>"
                            class="relative text-gray-700 hover:text-purple-600 transition-colors duration-200"
                            aria-label="View Cart">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <?php
                                $cartCount = 0;
                                $cart = \App\Models\Cart::where('user_id', Auth::id())->first();
                                if ($cart) {
                                    $cartCount = $cart->items->sum('quantity');
                                }
                            ?>
                            <?php if($cartCount > 0): ?>
                                <span
                                    class="absolute -top-2 -right-2 bg-purple-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"><?php echo e($cartCount); ?></span>
                            <?php endif; ?>
                        </a>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                class="flex items-center space-x-2 text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                                aria-label="User menu">
                                <span><?php echo e(auth()->user()->name); ?></span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false"
                                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="<?php echo e(route('user.dashboard')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Dashboard</a>
                                <a href="<?php echo e(route('user.purchases')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Purchases</a>
                                <a href="<?php echo e(route('user.profile')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                                <a href="<?php echo e(route('user.settings')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                <form method="POST" action="<?php echo e(route('logout')); ?>"
                                    class="border-t border-gray-100 mt-1 pt-1">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('cart.index')); ?>"
                            class="relative text-gray-700 hover:text-purple-600 transition-colors duration-200"
                            aria-label="View Cart">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <?php
                                $cartCount = 0;
                                $sessionId = session()->get('cart_session_id');
                                if ($sessionId) {
                                    $cart = \App\Models\Cart::where('session_id', $sessionId)->first();
                                    if ($cart) {
                                        $cartCount = $cart->items->sum('quantity');
                                    }
                                }
                            ?>
                            <?php if($cartCount > 0): ?>
                                <span
                                    class="absolute -top-2 -right-2 bg-purple-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"><?php echo e($cartCount); ?></span>
                            <?php endif; ?>
                        </a>
                        <a href="<?php echo e(route('login')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                            aria-label="Log in to Digitora">Log in</a>
                        <a href="<?php echo e(route('register')); ?>"
                            class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-5 py-2.5 rounded-full font-medium text-sm uppercase tracking-wider hover:shadow-lg transition-all duration-200"
                            aria-label="Sign up for Digitora">Sign up</a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Second Deck: Navigation Links -->
            <nav class="border-t border-gray-200">
                <div class="flex justify-between items-center h-12">
                    <!-- Desktop Navigation -->
                    <div class="custom-hidden-md custom-md-flex items-center space-x-10">
                        <a href="<?php echo e(route('user.browse')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                            aria-label="Browse Digital Products">Browse</a>
                        <a href="<?php echo e(route('user.browse')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                            aria-label="Explore Categories">Categories</a>
                        <a href="<?php echo e(route('seller.apply')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                            aria-label="Become a Seller">Sell</a>
                        <a href="<?php echo e(route('about')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                            aria-label="About Digitora">About</a>
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('user.dashboard')); ?>"
                                class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider transition-colors duration-200"
                                aria-label="Go to Dashboard">Dashboard</a>
                        <?php endif; ?>
                    </div>

                    <!-- Mobile Hamburger Menu Button -->
                    <button id="nav-menu-button"
                        class="md:hidden text-gray-700 hover:text-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-300 rounded"
                        aria-label="Toggle navigation menu">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>

                <!-- Mobile Navigation Menu -->
                <div id="nav-menu" class="md:hidden hidden py-4 border-t border-gray-200">
                    <div class="flex flex-col space-y-4 px-4">
                        <a href="<?php echo e(route('user.browse')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                            aria-label="Browse Digital Products">Browse</a>
                        <a href="<?php echo e(route('user.browse')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                            aria-label="Explore Categories">Categories</a>
                        <a href="<?php echo e(route('seller.apply')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                            aria-label="Become a Seller">Sell</a>
                        <a href="<?php echo e(route('about')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                            aria-label="About Digitora">About</a>
                        <a href="<?php echo e(route('cart.index')); ?>"
                            class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                            aria-label="View Cart">Cart</a>
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('user.dashboard')); ?>"
                                class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                                aria-label="Go to Dashboard">Dashboard</a>
                            <a href="<?php echo e(route('user.purchases')); ?>"
                                class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                                aria-label="View Purchases">My Purchases</a>
                            <a href="<?php echo e(route('user.profile')); ?>"
                                class="text-gray-700 hover:text-purple-600 font-medium text-sm uppercase tracking-wider py-2"
                                aria-label="Edit Profile">Profile</a>
                        <?php endif; ?>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-28 md:pt-32">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


    <!-- Footer -->
    

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Custom JS -->
    <script src="<?php echo e(asset(js_path() . '/digitora.js')); ?>" defer></script>
    <script src="<?php echo e(asset(js_path() . '/auth.js')); ?>" defer></script>
    <script src="<?php echo e(asset(js_path() . '/seller.js')); ?>" defer></script>
    <script src="<?php echo e(asset(js_path() . '/category-filter.js')); ?>" defer></script>
    <script src="<?php echo e(asset(js_path() . '/ai-chat.js')); ?>" defer></script>
</body>

</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/layouts/main.blade.php ENDPATH**/ ?>