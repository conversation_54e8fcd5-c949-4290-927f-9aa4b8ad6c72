<?php $__env->startSection('content'); ?>
    <div class="space-y-6">
        <div class="flex items-center gap-4">
            <a href="<?php echo e(route('seller.products.index')); ?>"
                class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Back</span>
            </a>
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Choose Product Type</h1>
                <p class="text-gray-600">Select the type of digital product you want to create</p>
            </div>
        </div>

        <div class="grid gap-6 md:grid-cols-3">
            <!-- Ebook -->
            <div class="group relative rounded-xl border-2 border-gray-200 bg-white p-6 shadow-sm hover:border-indigo-500 hover:shadow-lg transition-all duration-200 cursor-pointer"
                onclick="selectProductType('ebook')">
                <div class="flex flex-col items-center text-center space-y-4">
                    <div class="rounded-full bg-blue-100 p-4 group-hover:bg-blue-200 transition-colors">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">Ebook</h3>
                        <p class="text-sm text-gray-600 mt-2">Digital books, guides, manuals, and written content that customers can download and read.</p>
                    </div>
                    <div class="text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                        PDF, EPUB, DOC files
                    </div>
                </div>
            </div>

            <!-- Digital Product -->
            <div class="group relative rounded-xl border-2 border-gray-200 bg-white p-6 shadow-sm hover:border-indigo-500 hover:shadow-lg transition-all duration-200 cursor-pointer"
                onclick="selectProductType('digital')">
                <div class="flex flex-col items-center text-center space-y-4">
                    <div class="rounded-full bg-green-100 p-4 group-hover:bg-green-200 transition-colors">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">Digital Product</h3>
                        <p class="text-sm text-gray-600 mt-2">Software, templates, tools, graphics, audio files, and other downloadable digital assets.</p>
                    </div>
                    <div class="text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                        ZIP, Software, Templates
                    </div>
                </div>
            </div>

            <!-- Course/Tutorial -->
            <div class="group relative rounded-xl border-2 border-gray-200 bg-white p-6 shadow-sm hover:border-indigo-500 hover:shadow-lg transition-all duration-200 cursor-pointer"
                onclick="selectProductType('course')">
                <div class="flex flex-col items-center text-center space-y-4">
                    <div class="rounded-full bg-purple-100 p-4 group-hover:bg-purple-200 transition-colors">
                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">Course/Tutorial</h3>
                        <p class="text-sm text-gray-600 mt-2">Structured learning content with chapters, lessons, videos, and organized educational materials.</p>
                    </div>
                    <div class="text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                        Videos, Lessons, Materials
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="rounded-xl border bg-blue-50 p-6">
            <div class="flex items-start space-x-3">
                <svg class="h-6 w-6 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-blue-900">Need help choosing?</h4>
                    <div class="mt-2 text-sm text-blue-800 space-y-1">
                        <p><strong>Ebook:</strong> Best for written content like guides, manuals, or books</p>
                        <p><strong>Digital Product:</strong> Perfect for software, templates, graphics, or downloadable tools</p>
                        <p><strong>Course/Tutorial:</strong> Ideal for educational content with multiple lessons and structured learning</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectProductType(type) {
            // Redirect to the appropriate creation page based on type
            let createUrl = '';
            
            switch(type) {
                case 'ebook':
                    createUrl = '<?php echo e(route("seller.products.create")); ?>?type=ebook&content_type=simple';
                    break;
                case 'digital':
                    createUrl = '<?php echo e(route("seller.products.create")); ?>?type=digital&content_type=simple';
                    break;
                case 'course':
                    createUrl = '<?php echo e(route("seller.products.create-new-course")); ?>';
                    break;
            }
            
            if (createUrl) {
                window.location.href = createUrl;
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('seller.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/products/select-type.blade.php ENDPATH**/ ?>