<?php $__env->startPush('styles'); ?>
    <style>
        /* Enhanced Homepage Styles */
        :root {
            --digitora-primary: #6366f1;
            --digitora-secondary: #a855f7;
            --digitora-indigo: #4f46e5;
            --digitora-purple: #9333ea;
        }

        /* Category card styles */
        .category-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Enhanced card hover effects */
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Section dividers */
        .section-divider {
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            height: 1px;
            margin: 2rem 0;
        }

        /* Enhanced gradient backgrounds */
        .hero-gradient {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .section-bg-alternate {
            background: linear-gradient(135deg, #fafafa 0%, #f4f4f5 100%);
        }

        /* Improved text gradients */
        .text-gradient-primary {
            background: linear-gradient(135deg, var(--digitora-primary), var(--digitora-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-gradient-indigo {
            background: linear-gradient(135deg, var(--digitora-indigo), var(--digitora-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        .animation-delay-200 {
            animation-delay: 0.2s;
        }

        .animation-delay-400 {
            animation-delay: 0.4s;
        }

        .animation-delay-600 {
            animation-delay: 0.6s;
        }

        .animation-delay-800 {
            animation-delay: 0.8s;
        }

        /* Responsive improvements */
        @media (max-width: 640px) {
            .hero-text h1 {
                font-size: 2.5rem;
                line-height: 1.1;
            }

            .section-icon {
                width: 1.5rem;
                height: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .grid-responsive {
                grid-template-columns: repeat(1, minmax(0, 1fr));
                gap: 1rem;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .grid-responsive {
                grid-template-columns: repeat(2, minmax(0, 1fr));
                gap: 1.5rem;
            }
        }

        @media (min-width: 1025px) {
            .grid-responsive {
                grid-template-columns: repeat(4, minmax(0, 1fr));
                gap: 1.5rem;
            }
        }

        /* Enhanced button styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--digitora-primary), var(--digitora-secondary));
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 9999px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: var(--digitora-primary);
            border: 2px solid var(--digitora-primary);
            padding: 0.75rem 2rem;
            border-radius: 9999px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--digitora-primary);
            color: white;
            transform: translateY(-2px);
        }

        /* Line clamp utility */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Enhanced focus states for accessibility */
        .focus-ring:focus {
            outline: 2px solid var(--digitora-primary);
            outline-offset: 2px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Enhanced SEO Metadata -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Digitora",
        "url": "<?php echo e(url('/')); ?>",
        "logo": "<?php echo e(asset('images/digitora-logo.png')); ?>",
        "description": "Indonesia's premier digital marketplace and learning platform for creators and entrepreneurs to buy, sell, and learn through high-quality digital products and online courses.",
        "foundingDate": "2024",
        "foundingLocation": {
            "@type": "Place",
            "name": "Indonesia"
        },
        "areaServed": {
            "@type": "Country",
            "name": "Indonesia"
        },
        "knowsAbout": [
            "Digital Products",
            "Online Courses",
            "E-learning",
            "Digital Marketplace",
            "UMKM",
            "Indonesian Entrepreneurs"
        ],
        "sameAs": [
            "https://twitter.com/digitora",
            "https://facebook.com/digitora",
            "https://instagram.com/digitora",
            "https://linkedin.com/company/digitora"
        ]
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "<?php echo e(url('/')); ?>",
        "name": "Digitora - Digital Marketplace & Learning Platform Indonesia",
        "alternateName": "Digitora Indonesia",
        "description": "Discover and sell high-quality digital products, learn through online courses, and explore creator stores. Indonesia's leading platform for digital entrepreneurs and learners.",
        "inLanguage": "id-ID",
        "audience": {
            "@type": "Audience",
            "audienceType": "Indonesian Entrepreneurs, Digital Creators, Online Learners"
        },
        "potentialAction": [
            {
                "@type": "SearchAction",
                "target": "<?php echo e(route('browse.products')); ?>?search={search_term_string}",
                "query-input": "required name=search_term_string"
            },
            {
                "@type": "SearchAction",
                "target": "<?php echo e(route('browse.courses')); ?>?search={search_term_string}",
                "query-input": "required name=search_term_string"
            }
        ]
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Digitora - Digital Marketplace & Learning Platform Indonesia",
        "description": "Explore premium digital products, online courses, and creator stores. Join Indonesia's leading platform for digital entrepreneurs and learners.",
        "url": "<?php echo e(url('/')); ?>",
        "mainEntity": {
            "@type": "ItemList",
            "name": "Featured Content Categories",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "item": {
                        "@type": "Thing",
                        "name": "Digital Products",
                        "description": "Premium digital assets including eBooks, templates, graphics, and tools",
                        "url": "<?php echo e(route('browse.products')); ?>"
                    }
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "item": {
                        "@type": "Thing",
                        "name": "Online Courses",
                        "description": "Comprehensive online courses from Indonesian experts for UMKM and professionals",
                        "url": "<?php echo e(route('browse.courses')); ?>"
                    }
                },
                {
                    "@type": "ListItem",
                    "position": 3,
                    "item": {
                        "@type": "Thing",
                        "name": "Creator Stores",
                        "description": "Specialized collections from Indonesian creators and entrepreneurs",
                        "url": "<?php echo e(route('browse.stores')); ?>"
                    }
                }
            ]
        }
    }
    </script>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-purple-50 to-indigo-50 py-20 md:py-20 overflow-hidden">
        <div class="absolute inset-0 opacity-30">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center max-w-4xl mx-auto hero-text">
                <span
                    class="inline-block bg-purple-100 text-purple-700 text-sm px-5 py-2 rounded-full mb-6 font-medium animate-fade-in">Premier
                    Digital Marketplace for Creators</span>
                <h1
                    class="text-4xl sm:text-5xl md:text-6xl font-extrabold text-gray-900 mb-6 leading-tight animate-fade-in-up">
                    <span class="block">Discover & Sell Premium</span>
                    <span class="bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">Digital Products & Courses</span>
                </h1>
                <p class="text-lg md:text-xl text-gray-600 mb-10 max-w-2xl mx-auto animate-fade-in-up animation-delay-200">
                    Unlock a world of premium digital assets including eBooks, templates, online courses, and creative resources.
                    Join creators and entrepreneurs from across Indonesia on Digitora's comprehensive marketplace platform.</p>
                <div
                    class="flex flex-col sm:flex-row justify-center sm:space-x-5 space-y-4 sm:space-y-0 animate-fade-in-up animation-delay-400">
                    <a href="<?php echo e(route('user.browse')); ?>" class="btn-primary flex items-center justify-center space-x-2">
                        <span>Explore Now</span>
                        <svg class="h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-200"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                    <a href="<?php echo e(route('seller.apply')); ?>" class="btn-secondary">Start Selling</a>
                </div>

                <!-- Browse Navigation Buttons -->
                <div class="mt-12 animate-fade-in-up animation-delay-600">
                    <p class="text-center text-gray-600 mb-6 font-medium">Browse by Category</p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4 max-w-2xl mx-auto">
                        <a href="<?php echo e(route('browse.products')); ?>"
                           class="bg-white bg-opacity-90 backdrop-filter backdrop-blur-md rounded-lg px-6 py-3 text-center shadow-sm hover:shadow-md transition-all duration-300 hover:bg-opacity-100 group">
                            <div class="flex items-center justify-center space-x-2">
                                <svg class="h-5 w-5 text-purple-600 group-hover:text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span class="font-medium text-gray-700 group-hover:text-gray-900">Digital Products</span>
                            </div>
                        </a>
                        <a href="<?php echo e(route('browse.courses')); ?>"
                           class="bg-white bg-opacity-90 backdrop-filter backdrop-blur-md rounded-lg px-6 py-3 text-center shadow-sm hover:shadow-md transition-all duration-300 hover:bg-opacity-100 group">
                            <div class="flex items-center justify-center space-x-2">
                                <svg class="h-5 w-5 text-purple-600 group-hover:text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <span class="font-medium text-gray-700 group-hover:text-gray-900">Online Courses</span>
                            </div>
                        </a>
                        <a href="<?php echo e(route('browse.stores')); ?>"
                           class="bg-white bg-opacity-90 backdrop-filter backdrop-blur-md rounded-lg px-6 py-3 text-center shadow-sm hover:shadow-md transition-all duration-300 hover:bg-opacity-100 group">
                            <div class="flex items-center justify-center space-x-2">
                                <svg class="h-5 w-5 text-purple-600 group-hover:text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span class="font-medium text-gray-700 group-hover:text-gray-900">Creator Stores</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Stats -->
            

            <!-- Trust Badges -->
            
        </div>
    </section>

    <!-- Popular Categories -->
    

    <!-- Platform Stats -->
    

    <!-- Three Main Content Sections -->

    <!-- Digital Products Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <div class="flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 animate-fade-in">Premium Digital Products</h2>
                </div>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">
                    Discover handpicked digital assets including eBooks, templates, graphics, and tools from Indonesian creators designed to boost your productivity and creativity.
                </p>
            </div>

            <!-- Structured Data for Products -->
            <script type="application/ld+json">
                {
                    "@context": "https://schema.org",
                    "@type": "ItemList",
                    "name": "Featured Digital Products",
                    "itemListElement": [
                        <?php $__currentLoopData = $featuredProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            {
                                "@type": "ListItem",
                                "position": <?php echo e($index + 1); ?>,
                                "item": {
                                    "@type": "Product",
                                    "name": "<?php echo e($product['name']); ?>",
                                    "image": "<?php echo e($product['image']); ?>",
                                    "description": "A <?php echo e($product['detailed_category'] ?? $product['subcategory'] ?? $product['category']); ?> digital product by <?php echo e($product['creator']); ?> on Digitora.",
                                    "offers": {
                                        "@type": "Offer",
                                        "price": <?php echo e($product['price']); ?>,
                                        "priceCurrency": "IDR",
                                        "availability": "https://schema.org/InStock"
                                    },
                                    "aggregateRating": {
                                        "@type": "AggregateRating",
                                        "ratingValue": <?php echo e($product['rating']); ?>,
                                        "reviewCount": <?php echo e($product['reviews']); ?>

                                    }
                                }
                            }<?php if(!$loop->last): ?>,<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ]
                }
            </script>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
                <?php if(count($featuredProducts) == 0): ?> style="display: none;" <?php endif; ?>>
                <?php $__empty_1 = true; $__currentLoopData = $featuredProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <article
                        class="bg-white rounded-xl border border-gray-100 overflow-hidden shadow-sm card-hover animate-on-scroll"
                        style="animation-delay: <?php echo e($loop->index * 100); ?>ms">
                        <div class="relative">
                            <img src="<?php echo e($product['image']); ?>"
                                alt="<?php echo e($product['name']); ?> by <?php echo e($product['creator']); ?>"
                                class="w-full h-48 object-cover">
                            <?php if($product['discount_price']): ?>
                                <div
                                    class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                    <?php echo e($product['discount_percentage']); ?>% OFF
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1 line-clamp-2"><?php echo e($product['name']); ?></h3>
                            <div class="flex justify-between items-center mb-2">
                                <p class="text-gray-500 text-sm">by <?php echo e($product['creator']); ?></p>
                                <span
                                    class="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full"><?php echo e($product['detailed_category'] ?? ($product['subcategory'] ?? $product['category'])); ?></span>
                            </div>
                            <div class="flex justify-between items-center mb-4">
                                <?php if($product['discount_price']): ?>
                                    <div>
                                        <p class="text-gray-500 text-xs line-through">IDR
                                            <?php echo e(number_format($product['price'], 0, ',', '.')); ?></p>
                                        <p class="text-purple-600 font-bold">IDR
                                            <?php echo e(number_format($product['discount_price'], 0, ',', '.')); ?></p>
                                    </div>
                                <?php else: ?>
                                    <p class="text-purple-600 font-bold">IDR
                                        <?php echo e(number_format($product['price'], 0, ',', '.')); ?></p>
                                <?php endif; ?>
                                <div class="flex items-center">
                                    <span class="text-yellow-400 mr-1">★</span>
                                    <span class="text-gray-600 text-sm"><?php echo e($product['rating']); ?>

                                        (<?php echo e($product['reviews']); ?>)
                                    </span>
                                </div>
                            </div>
                            <a href="<?php echo e(route('store.product', ['storeNameSlug' => $product['store_name_slug'], 'product' => $product['slug']])); ?>"
                                class="block text-center bg-purple-600 text-white px-4 py-2 rounded-full hover:bg-purple-700 transition-colors duration-300 font-medium text-sm"
                                aria-label="View details of <?php echo e($product['name']); ?>">View Details</a>
                        </div>
                    </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <svg class="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Featured Products Yet</h3>
                        <p class="text-gray-500">Check back soon for amazing digital products from our creators!</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center animate-fade-in-up animation-delay-400">
                <a href="<?php echo e(route('browse.products')); ?>"
                    class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700 transition-colors duration-300 group">
                    <span>View All Digital Products</span>
                    <svg class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Online Courses Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <div class="flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 animate-fade-in">Online Courses</h2>
                </div>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">
                    Master new skills with comprehensive online courses from Indonesian experts. Learn at your own pace with structured content designed for UMKM and professionals.
                </p>
            </div>

            <!-- Structured Data for Courses -->
            <script type="application/ld+json">
                {
                    "@context": "https://schema.org",
                    "@type": "ItemList",
                    "name": "Featured Online Courses",
                    "itemListElement": [
                        <?php $__currentLoopData = $featuredCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            {
                                "@type": "ListItem",
                                "position": <?php echo e($index + 1); ?>,
                                "item": {
                                    "@type": "Course",
                                    "name": "<?php echo e($course['title']); ?>",
                                    "description": "<?php echo e($course['short_description'] ?? 'Learn ' . $course['title'] . ' with expert instruction from ' . $course['instructor']); ?>",
                                    "provider": {
                                        "@type": "Organization",
                                        "name": "Digitora",
                                        "url": "<?php echo e(url('/')); ?>"
                                    },
                                    "instructor": {
                                        "@type": "Person",
                                        "name": "<?php echo e($course['instructor']); ?>"
                                    },
                                    "offers": {
                                        "@type": "Offer",
                                        "price": <?php echo e($course['price']); ?>,
                                        "priceCurrency": "IDR",
                                        "availability": "https://schema.org/InStock"
                                    }
                                }
                            }<?php if(!$loop->last): ?>,<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ]
                }
            </script>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
                <?php if(count($featuredCourses) == 0): ?> style="display: none;" <?php endif; ?>>
                <?php $__empty_1 = true; $__currentLoopData = $featuredCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <article
                        class="bg-white rounded-xl border border-gray-100 overflow-hidden shadow-sm card-hover animate-on-scroll"
                        style="animation-delay: <?php echo e($loop->index * 100); ?>ms">
                        <div class="relative">
                            <img src="<?php echo e($course['thumbnail']); ?>"
                                alt="<?php echo e($course['title']); ?> by <?php echo e($course['instructor']); ?>"
                                class="w-full h-48 object-cover">
                            <?php if($course['discount_price']): ?>
                                <div
                                    class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                    <?php echo e(round((($course['price'] - $course['discount_price']) / $course['price']) * 100)); ?>% OFF
                                </div>
                            <?php endif; ?>
                            <div class="absolute top-2 left-2 bg-indigo-600 text-white text-xs font-bold px-2 py-1 rounded">
                                <?php echo e($course['difficulty']); ?>

                            </div>
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1 line-clamp-2"><?php echo e($course['title']); ?></h3>
                            <div class="flex justify-between items-center mb-2">
                                <p class="text-gray-500 text-sm">by <?php echo e($course['instructor']); ?></p>
                                <span
                                    class="text-xs px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full"><?php echo e($course['category']); ?></span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo e($course['duration']); ?>

                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                    </svg>
                                    <?php echo e($course['students']); ?> students
                                </div>
                            </div>
                            <div class="flex justify-between items-center mb-4">
                                <?php if($course['discount_price']): ?>
                                    <div>
                                        <p class="text-gray-500 text-xs line-through">IDR
                                            <?php echo e(number_format($course['price'], 0, ',', '.')); ?></p>
                                        <p class="text-indigo-600 font-bold">IDR
                                            <?php echo e(number_format($course['discount_price'], 0, ',', '.')); ?></p>
                                    </div>
                                <?php else: ?>
                                    <p class="text-indigo-600 font-bold">IDR
                                        <?php echo e(number_format($course['price'], 0, ',', '.')); ?></p>
                                <?php endif; ?>
                                <div class="flex items-center">
                                    <span class="text-yellow-400 mr-1">★</span>
                                    <span class="text-gray-600 text-sm"><?php echo e($course['rating']); ?></span>
                                </div>
                            </div>
                            <a href="<?php echo e(route('browse.courses.show', $course['slug'])); ?>"
                                class="block text-center bg-indigo-600 text-white px-4 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300 font-medium text-sm"
                                aria-label="View details of <?php echo e($course['title']); ?>">View Course</a>
                        </div>
                    </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <svg class="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Featured Courses Yet</h3>
                        <p class="text-gray-500">Check back soon for amazing courses from our expert instructors!</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center animate-fade-in-up animation-delay-400">
                <a href="<?php echo e(route('browse.courses')); ?>"
                    class="inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-700 transition-colors duration-300 group">
                    <span>View All Courses</span>
                    <svg class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Creator Stores Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <div class="flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 animate-fade-in">Creator Stores</h2>
                </div>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">
                    Discover our top Indonesian creators and their specialized collections of digital products and courses. Support local talent and find unique content.
                </p>
            </div>

            <!-- Structured Data for Stores -->
            <script type="application/ld+json">
                {
                    "@context": "https://schema.org",
                    "@type": "ItemList",
                    "name": "Featured Creator Stores",
                    "itemListElement": [
                        <?php $__currentLoopData = $featuredStores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            {
                                "@type": "ListItem",
                                "position": <?php echo e($index + 1); ?>,
                                "item": {
                                    "@type": "Store",
                                    "name": "<?php echo e($store['name']); ?>",
                                    "description": "<?php echo e($store['description']); ?>",
                                    "url": "<?php echo e(url('/' . $store['slug'])); ?>",
                                    "image": "<?php echo e($store['logo'] ? asset('storage/' . $store['logo']) : ''); ?>"
                                }
                            }<?php if(!$loop->last): ?>,<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ]
                }
            </script>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
                <?php if(count($featuredStores) == 0): ?> style="display: none;" <?php endif; ?>>
                <?php $__empty_1 = true; $__currentLoopData = $featuredStores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <article
                        class="bg-white rounded-xl border border-gray-100 overflow-hidden shadow-sm card-hover animate-on-scroll"
                        style="animation-delay: <?php echo e($loop->index * 100); ?>ms">
                        <div class="relative">
                            <?php if($store['logo'] && Storage::exists('public/' . $store['logo'])): ?>
                                <img src="<?php echo e(asset('storage/' . $store['logo'])); ?>" alt="<?php echo e($store['name']); ?>"
                                    class="w-full h-48 object-cover">
                            <?php else: ?>
                                <div
                                    class="w-full h-48 bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center">
                                    <span class="text-7xl font-bold text-white"><?php echo e(substr($store['name'], 0, 1)); ?></span>
                                </div>
                            <?php endif; ?>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                            <div class="absolute bottom-3 left-3 flex items-center">
                                <?php if($store['user_avatar'] && Storage::exists('public/' . $store['user_avatar'])): ?>
                                    <img src="<?php echo e(asset('storage/' . $store['user_avatar'])); ?>"
                                        alt="<?php echo e($store['user_name']); ?>"
                                        class="w-10 h-10 rounded-full border-2 border-white mr-2">
                                <?php else: ?>
                                    <div
                                        class="w-10 h-10 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold text-lg border-2 border-white mr-2">
                                        <?php echo e(substr($store['user_name'], 0, 1)); ?>

                                    </div>
                                <?php endif; ?>
                                <h3 class="text-white font-bold text-lg"><?php echo e($store['name']); ?></h3>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full">
                                    <?php echo e($store['main_category']['name'] ?? 'Digital Products'); ?>

                                </span>
                                <span class="text-gray-500 text-sm"><?php echo e($store['product_count']); ?> products</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($store['description']); ?></p>
                            <a href="/<?php echo e($store['slug']); ?>"
                                class="block text-center bg-purple-600 text-white px-4 py-2 rounded-full hover:bg-purple-700 transition-colors duration-300 font-medium text-sm"
                                aria-label="Visit <?php echo e($store['name']); ?>">Visit Store</a>
                        </div>
                    </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <svg class="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Featured Stores Yet</h3>
                        <p class="text-gray-500">Check back soon for amazing stores from our talented creators!</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center animate-fade-in-up animation-delay-400">
                <a href="<?php echo e(route('browse.stores')); ?>"
                    class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700 transition-colors duration-300 group">
                    <span>View All Stores</span>
                    <svg class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- How Digitora Works -->
    <section class="py-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">How Digitora Works</h2>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Simple steps to buy
                    or sell premium digital products and courses on Indonesia's leading marketplace platform for creators and entrepreneurs.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto">
                <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="text-center animate-on-scroll" style="animation-delay: <?php echo e($step['step'] * 100); ?>ms">
                        <div class="flex justify-center mb-5">
                            <span
                                class="bg-purple-100 text-purple-700 text-xl font-semibold rounded-full w-12 h-12 flex items-center justify-center shadow-sm"><?php echo e($step['step']); ?></span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($step['title']); ?></h3>
                        <p class="text-gray-600 text-sm leading-relaxed"><?php echo e($step['description']); ?></p>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <?php if(count($testimonials) > 0): ?>
        <section class="py-20 bg-gradient-to-r from-purple-50 to-indigo-50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">What Our Community
                        Says</h2>
                    <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Hear from our
                        Indonesian community of creators and learners who trust Digitora for their digital products and online learning
                        needs.</p>
                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-<?php echo e(count($testimonials) >= 3 ? '3' : (count($testimonials) == 2 ? '2' : '1')); ?> gap-8 max-w-5xl mx-auto">
                    <!-- Testimonials from real reviews -->
                    <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white p-6 rounded-xl border border-gray-100 shadow-sm card-hover animate-on-scroll"
                            style="animation-delay: <?php echo e($loop->index * 100); ?>ms">
                            <div class="flex items-center mb-3">
                                <div class="flex">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= $testimonial['rating']): ?>
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        <?php else: ?>
                                            <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                            </div>
                            <p class="text-gray-600 italic mb-5 text-lg leading-relaxed">"<?php echo e($testimonial['quote']); ?>"</p>
                            <div class="flex items-center">
                                <div
                                    class="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center text-purple-700 font-bold text-xl mr-3">
                                    <?php echo e(substr($testimonial['author'], 0, 1)); ?>

                                </div>
                                <div>
                                    <p class="text-gray-900 font-semibold"><?php echo e($testimonial['author']); ?></p>
                                    <p class="text-gray-500 text-sm"><?php echo e($testimonial['role']); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- Final CTA -->
    <section class="py-20 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-center">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-extrabold mb-4 animate-fade-in">Ready to Join Digitora?</h2>
            <p class="text-lg mb-8 max-w-2xl mx-auto animate-fade-in animation-delay-200">Be part of Indonesia's thriving
                community of creators and learners on our comprehensive digital marketplace and learning platform.</p>
            <div
                class="flex flex-col sm:flex-row justify-center sm:space-x-5 space-y-4 sm:space-y-0 animate-fade-in-up animation-delay-400">
                <a href="<?php echo e(route('seller.apply')); ?>"
                    class="bg-white text-purple-600 px-8 py-3 rounded-full font-medium hover:bg-gray-100 transition-all duration-300 shadow-sm hover:shadow">Start
                    Selling Today</a>
                <a href="<?php echo e(route('browse.products')); ?>"
                    class="border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-purple-600 transition-all duration-300">Explore
                    Marketplace</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-12 border-t">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center space-x-3 mb-4"
                        aria-label="Digitora Homepage">
                        <div
                            class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white w-10 h-10 rounded-lg flex items-center justify-center text-2xl font-bold">
                            D</div>
                        <span class="text-2xl font-extrabold text-gray-900 tracking-tight">Digitora</span>
                    </a>
                    <p class="text-gray-600 text-sm leading-relaxed">Empowering creators and entrepreneurs worldwide with a
                        seamless platform to buy and sell high-quality digital products.</p>
                </div>
                

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Links</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="<?php echo e(route('user.browse')); ?>"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Browse Digital Products">Browse Products</a></li>
                        <li><a href="<?php echo e(route('seller.apply')); ?>"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Become a Seller">Become a Seller</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Pricing Information">Pricing</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Success Stories">Success Stories</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Company</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="About Digitora">About Us</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Digitora Blog">Blog</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Careers at Digitora">Careers</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Contact Digitora">Contact Us</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Support</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Help Center">Help Center</a></li>
                        <li><a href="<?php echo e(route('terms.alt')); ?>"
                                class="hover:text-purple-600 transition-colors duration-200" aria-label="Terms of Service"
                                target="_blank">Terms of Service</a></li>
                        <li><a href="<?php echo e(route('privacy.alt')); ?>"
                                class="hover:text-purple-600 transition-colors duration-200" aria-label="Privacy Policy"
                                target="_blank">Privacy Policy</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Frequently Asked Questions">FAQ</a></li>
                    </ul>
                </div>
            </div>
        </div>


        <div class="mt-12 text-center text-gray-600 text-sm">
            <p>&copy; <?php echo e(date('Y')); ?> Digitora. All rights reserved.</p>
        </div>
        </div>
    </footer>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- All scripts moved to public/dev-js/digitora.js -->
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/welcome.blade.php ENDPATH**/ ?>